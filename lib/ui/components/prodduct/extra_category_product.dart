import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ExtraCategoryProduct extends ConsumerStatefulWidget {
  const ExtraCategoryProduct({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ExtraCategoryProductState();
}

class _ExtraCategoryProductState extends ConsumerState<ExtraCategoryProduct> {
  final List<ProductModel> _productsByCaterory = [];

  int _extraCategoryIndex = -1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getExtraCategoryProducts();
    });
  }

  getExtraCategoryProducts({String? category}) async {
    final r = await ref.read(productVm).getProductsByCategoryFilter(
          category: category,
        );

    if (r.success) {
      _productsByCaterory.clear();
      _productsByCaterory.addAll(r.data!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(children: [
            ...List.generate(
              4,
              (i) {
                return HomeTabOutline(
                  text: [
                    "Best sellers",
                    "Recommended",
                    "New arrivals",
                    "Discount",
                  ][i],
                  isSelected: _extraCategoryIndex == i,
                  onTap: () {
                    _extraCategoryIndex = i;
                    setState(() {});
                    ref.read(productVm).getProductsByCategoryFilter(
                          extraCategory: [
                            "bestsellers",
                            "recommended",
                            "newarrival",
                            "discount",
                          ][i],
                        );
                  },
                );
              },
            ),
          ]),
        ),
        Builder(builder: (context) {
          if (productRef.busy(productsByCategoryFilter)) {
            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.height(100),
              ),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              crossAxisCount: 2,
              childAspectRatio: 0.68,
              children: List.generate(
                20,
                (i) => Skeletonizer(
                  enabled: true,
                  child: HomeProductCard(
                    product: ProductModel(
                      name: "Glenfiddich 18yrs",
                      variations: [
                        Variation(
                          volume: "75cl",
                          unitPrice: 379500,
                          category: "BEST SELLER",
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }
          return GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              bottom: Sizer.height(100),
            ),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            crossAxisCount: 2,
            childAspectRatio: 0.64,
            children: List.generate(
              _productsByCaterory.length,
              (i) => HomeProductCard(
                product: _productsByCaterory[i],
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.productDetailsScreen,
                    arguments: _productsByCaterory[i],
                  );
                },
              ),
            ),
          );
        }),
      ],
    );
  }
}
