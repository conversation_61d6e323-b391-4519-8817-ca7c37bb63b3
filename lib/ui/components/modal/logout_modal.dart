import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class LogoutModal extends StatefulWidget {
  const LogoutModal({
    super.key,
    required this.onConfirm,
    this.isBusy = false,
  });

  final VoidCallback onConfirm;
  final bool isBusy;

  @override
  State<LogoutModal> createState() => _LogoutModalState();
}

class _LogoutModalState extends State<LogoutModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          Icon(
            Iconsax.logout,
            color: AppColors.primaryBlack,
            size: Sizer.radius(60),
          ),
          const YBox(16),
          Text(
            "Logout",
            textAlign: TextAlign.center,
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(16),
          CustomBtn.solid(
            onTap: () {
              Navigator.pop(context);
            },
            online: true,
            isLoading: widget.isBusy,
            text: "Cancel",
          ),
          const YBox(10),
          CustomBtn.solid(
            onTap: widget.onConfirm,
            online: true,
            isLoading: widget.isBusy,
            isOutline: true,
            outlineColor: AppColors.primaryBlack,
            textColor: AppColors.primaryBlack,
            text: "Yes, Logout",
          ),
          const YBox(40),
        ],
      ),
    );
  }
}
