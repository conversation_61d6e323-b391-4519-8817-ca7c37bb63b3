import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';
import 'package:flutter/gestures.dart';

class WelcomeBackScreen extends ConsumerStatefulWidget {
  const WelcomeBackScreen({super.key});

  @override
  ConsumerState<WelcomeBackScreen> createState() => _WelcomeBackScreenState();
}

class _WelcomeBackScreenState extends ConsumerState<WelcomeBackScreen> {
  final phoneC = TextEditingController();
  final phoneF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, phoneF);
    phoneF.requestFocus();
  }

  @override
  void dispose() {
    phoneC.dispose();
    phoneF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authRef = ref.watch(authVm);
    return BusyOverlay(
      show: authRef.isBusy,
      child: Scaffold(
        appBar: const CustomAppbar(
          title: "",
        ),
        body: ListView(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          children: [
            imageHelper(
              AppImages.logoBlack,
              height: Sizer.height(48),
            ),
            const YBox(30),
            Text(
              "Welcome back",
              textAlign: TextAlign.center,
              style: AppTypography.text24.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const YBox(4),
            Text(
              "Login to your account to continue shopping your \nfavourite drinks",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.black70,
              ),
            ),
            const YBox(40),
            CustomTextField(
              controller: phoneC,
              focusNode: phoneF,
              hintText: "enter phone number",
              labelText: "Phone number",
              showLabelHeader: true,
              borderRadius: 0,
              keyboardType: KeyboardType.phone,
              errorText: (phoneF.hasFocus && phoneC.text.trim().length < 11)
                  ? "Phone number must be at least 11 digits"
                  : null,
              onChanged: (p0) => authRef.reBuildUI(),
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(10)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "NG",
                      style: AppTypography.text16.copyWith(),
                    ),
                    Icon(
                      Icons.keyboard_arrow_down,
                      size: Sizer.height(24),
                      color: AppColors.black70,
                    )
                  ],
                ),
              ),
            ),
            const YBox(24),
            CustomBtn.solid(
              onTap: () async {
                FocusScope.of(context).unfocus();
                final r = await authRef.login(
                    args: AuthArg(
                  username: phoneC.text.trim(),
                ));
                handleApiResponse(
                  response: r,
                  onSuccess: () {
                    Navigator.pushNamed(context, RoutePath.otpScreen);
                  },
                );
              },
              online: phoneC.text.trim().length == 11,
              text: "Continue",
            ),
            const YBox(16),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Old user? sign in with",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " email and password",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pushNamed(context, RoutePath.loginScreen);
                      },
                  ),
                ],
              ),
            ),
            const YBox(24),
            Text(
              "OR",
              textAlign: TextAlign.center,
              style: AppTypography.text14.copyWith(
                color: AppColors.black70,
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(24),
            SocialBtn(
              iconPath: AppSvgs.google,
              btnText: "Sign up with Google",
              onTap: () {},
            ),
            const YBox(12),
            SocialBtn(
              iconPath: AppSvgs.apple,
              btnText: "Sign up with Apple",
              onTap: () {},
            ),
            const YBox(20),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "Don’t have an account? ",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black70,
                      fontFamily: 'GeneralSans',
                    ),
                  ),
                  TextSpan(
                    text: " Sign up",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.primaryBlack,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'GeneralSans',
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        Navigator.pushNamed(context, RoutePath.registerScreen);
                      },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
