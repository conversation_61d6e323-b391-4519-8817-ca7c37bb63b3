import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class WishlistScreen extends ConsumerStatefulWidget {
  const WishlistScreen({super.key});

  @override
  ConsumerState<WishlistScreen> createState() => _WishlistScreenState();
}

class _WishlistScreenState extends ConsumerState<WishlistScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartVm.notifier).getWishlist();
    });
  }

  @override
  Widget build(BuildContext context) {
    final cartRef = ref.watch(cartVm);
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(Sizer.height(60)),
        child: Container(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: Sizer.height(20),
                        color: AppColors.black70,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: Sizer.width(20)),
                    child: Text(
                      'Wishlist',
                      style: AppTypography.text18.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Container()
                ],
              ),
              const YBox(16),
            ],
          ),
        ),
      ),
      body: LoadableContentBuilder(
        isBusy: cartRef.busy(getWishlistState),
        items: cartRef.wishList,
        loadingBuilder: (context) {
          return const SizerLoader(height: 700);
        },
        emptyBuilder: (context) {
          return Center(
            child: Text(
              "No items in wishlist",
              style: AppTypography.text18.copyWith(
                  fontWeight: FontWeight.w500, color: AppColors.black70),
            ),
          );
        },
        contentBuilder: (context) {
          return ListView.separated(
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              top: Sizer.width(16),
              bottom: Sizer.width(100),
            ),
            itemBuilder: (ctx, i) {
              final w = cartRef.wishList[i];
              return CartProductCard(
                showRemoveBtn: true,
                cartItem: w,
                onRemoveTap: () {
                  printty("remove from wishlist: ${w.productId}");
                  final loadingProvider = StateProvider<bool>((ref) => false);

                  ModalWrapper.bottomSheet(
                    context: context,
                    widget: Consumer(
                      builder: (context, ref, child) {
                        final isLoading = ref.watch(loadingProvider);

                        return ConfirmModal(
                          title: "Remove from wishlist",
                          subtitle:
                              "Are you sure you want to remove this item from wishlist",
                          rightBtnText: "Remove",
                          isLoading: isLoading,
                          rightBtnTap: () async {
                            // Set loading to true
                            ref.read(loadingProvider.notifier).state = true;

                            try {
                              await ref
                                  .read(cartVm.notifier)
                                  .removeCartOrWishlist(
                                    itemId: w.id ?? "",
                                    isWishList: true,
                                  );
                            } finally {
                              if (context.mounted) {
                                ref.read(loadingProvider.notifier).state =
                                    false;
                                Navigator.pop(context);
                              }
                            }
                          },
                        );
                      },
                    ),
                  );
                },
              );
            },
            separatorBuilder: (_, __) => Padding(
              padding: EdgeInsets.symmetric(
                vertical: Sizer.height(10),
              ),
              child: const Divider(color: AppColors.grayE6),
            ),
            itemCount: cartRef.wishList.length,
          );
        },
      ),
    );
  }
}
