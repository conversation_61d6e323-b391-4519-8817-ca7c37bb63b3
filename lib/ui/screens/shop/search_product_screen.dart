import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class SearchProductScreen extends ConsumerStatefulWidget {
  const SearchProductScreen({super.key});

  @override
  ConsumerState<SearchProductScreen> createState() =>
      _SearchProductScreenState();
}

class _SearchProductScreenState extends ConsumerState<SearchProductScreen> {
  final _searchC = TextEditingController();
  final _searchF = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _searchF);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchF.requestFocus();
    });
  }

  @override
  dispose() {
    _searchC.dispose();
    _searchF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final productRef = ref.watch(productVm);
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(Sizer.height(60)),
        child: Container(
          padding: EdgeInsets.only(
            left: Sizer.width(16),
            right: Sizer.width(16),
          ),
          decoration: BoxDecoration(
            color: AppColors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black12.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      productRef.clearSearchProducts();
                    },
                    child: Padding(
                      padding: EdgeInsets.all(Sizer.radius(4)),
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: Sizer.height(20),
                        color: AppColors.black70,
                      ),
                    ),
                  ),
                  const XBox(8),
                  Expanded(
                    child: CustomTextField(
                      controller: _searchC,
                      focusNode: _searchF,
                      hintText: 'Find your favourite drinks',
                      fillColor: AppColors.transparent,
                      hideBorder: true,
                      borderRadius: 0,
                      onChanged: (p0) {
                        ref.read(productVm).productsBySearch(query: p0.trim());
                      },
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      _searchC.clear();
                      _searchF.unfocus();
                      productRef.clearSearchProducts();
                    },
                    child: const Icon(
                      Icons.close,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: LoadableContentBuilder(
                isBusy: productRef.busy(productSearchState),
                items: productRef.searchProducts,
                loadingBuilder: (context) {
                  return GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                      bottom: Sizer.height(100),
                    ),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    childAspectRatio: 0.68,
                    children: List.generate(
                      10,
                      (i) => Skeletonizer(
                        enabled: true,
                        child: HomeProductCard(
                          product: ProductModel(
                            name: "Glenfiddich 18yrs",
                            variations: [
                              Variation(
                                volume: "75cl",
                                unitPrice: 379500,
                                category: "BEST SELLER",
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
                emptyBuilder: (context) {
                  return Center(
                    child: Text(
                      "Start typing to search for products",
                      style: AppTypography.text18.copyWith(
                          fontWeight: FontWeight.w500,
                          color: AppColors.black70),
                    ),
                  );
                },
                contentBuilder: (context) {
                  return GridView.count(
                    padding: EdgeInsets.only(
                      left: Sizer.width(16),
                      right: Sizer.width(16),
                      bottom: Sizer.height(100),
                    ),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    crossAxisCount: 2,
                    childAspectRatio: 0.64,
                    children:
                        List.generate(productRef.searchProducts.length, (i) {
                      final sp = productRef.searchProducts[i];
                      return HomeProductCard(
                        product: sp,
                        onTap: () {
                          Navigator.pushNamed(
                            context,
                            RoutePath.productDetailsScreen,
                            arguments: sp,
                          );
                        },
                      );
                    }),
                  );
                }),
          ),
        ],
      ),
    );
  }
}
