import 'package:bottle_king_mobile/core/core.dart';

class AuthVm extends BaseVm {
  AuthUserModel? _user;
  AuthUserModel? get user => _user;

  String get fullNames => "${_user?.firstname} ${_user?.lastname}";

  Future<bool> loadUserFromStorage() async {
    final user = await StorageService.getUser();
    if (user != null) {
      _user = user;
      reBuildUI();
      return true;
    }
    return false;
  }

  Future<ApiResponse> createAccount({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String dob,
  }) async {
    return await performApiCall(
      url: "/auth/register",
      method: apiService.post,
      body: {
        "authType": "regular",
        "email": email,
        "phone": phone,
        "firstname": firstName,
        "lastname": lastName,
        // "dob": dob,
        "admin": false, //or false,
        "platform": "mobile",
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> login({
    required AuthArg args,
  }) async {
    final body = args.toMap();
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/auth/login",
      method: apiService.post,
      body: body,
      onSuccess: (data) {
        _user = authUserModelFromJson(json.encode(data["data"]["user"]));
        StorageService.storeAccessToken(data["data"]?["accessToken"]);
        StorageService.storeRefreshToken(data["data"]?["refreshToken"]);
        StorageService.storeUser(_user ?? AuthUserModel());
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getUser() async {
    return await performApiCall(
      url: "/customer",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        // String token = data["data"]["token"];
        // _user = authUserFromJson(json.encode(data["data"]["user"]));
        // StorageService.storeAccessToken(token);
        // StorageService.storeUser(_user ?? AuthUser());
        return apiResponse;
      },
    );
  }

  // Future<ApiResponse> loginWithGoogle({required String idToken}) async {
  //   return await performApiCall(
  //     url: "/user/auth/google",
  //     method: apiService.post,
  //     body: {
  //       "idToken": idToken,
  //     },
  //     onSuccess: (data) {
  //       printty("Data from loginWithGoogle: $data");
  //       String token = data["data"]["Token"];
  //       _user = User.fromJson(data["data"]);
  //       StorageService.storeAccessToken(token);
  //       StorageService.storeUser(_user ?? User());
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> requestOTP({required String email}) async {
  //   return await performApiCall(
  //     url: "/otp/send",
  //     method: apiService.post,
  //     body: {"email": email},
  //     onSuccess: (data) {
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> forgotPasswordRequest({required String email}) async {
  //   return await performApiCall(
  //     url: "/user/change-password-request",
  //     method: apiService.post,
  //     body: {"Email": email},
  //     onSuccess: (data) {
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> verifyOTP({required OtpArg args}) async {
  //   return await performApiCall(
  //     url: "/otp/verify",
  //     method: apiService.post,
  //     body: args.toMap(),
  //     onSuccess: (data) {
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> resetPassword({required OtpArg args}) async {
  //   return await performApiCall(
  //     url: "/user/new-password",
  //     method: apiService.post,
  //     body: args.toMap(),
  //     onSuccess: (data) {
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> getUserProfile() async {
  //   return await performApiCall(
  //     url: "/user/profile",
  //     method: apiService.getWithAuth,
  //     onSuccess: (data) {
  //       String token = data["data"]["Token"];
  //       _user = User.fromJson(data["data"]);
  //       StorageService.storeAccessToken(token);
  //       StorageService.storeUser(_user ?? User());
  //       return apiResponse;
  //     },
  //   );
  // }

  // Future<ApiResponse> updateUserProfile({
  //   String? fullName,
  //   String? dob,
  //   String? gender,
  //   File? profileImage,
  // }) async {
  //   final body = {
  //     "FullName": fullName,
  //     "dob": dob,
  //     "gender": gender,
  //   };
  //   if (profileImage != null) {
  //     body["file"] =
  //         (await MultipartFile.fromFile(profileImage.path)).toString();
  //   }

  //   body.removeWhere((k, v) => v == null || v.isEmpty);
  //   printty("updateUserProfile body: $body");
  //   return await performApiCall(
  //     url: "/user",
  //     method: apiService.putWithAuth,
  //     isFormData: true,
  //     body: body,
  //     onSuccess: (data) {
  //       return apiResponse;
  //     },
  //   );
  // }

  Future<ApiResponse> logout() async {
    try {
      setBusy(true);
      await Future.delayed(const Duration(seconds: 1));

      Navigator.pushNamedAndRemoveUntil(
        NavKey.appNavKey.currentContext!,
        RoutePath.welcomeScreen,
        (r) => false,
      );
      setBusy(false);
      String url = "/auth/logout";
      apiResponse = await apiService.postWithAuth(body: null, url: url);
      await StorageService.logout();

      return apiResponse;
    } catch (e) {
      printty(e.toString(), logName: "Logout Error");
      setBusy(false);
      return ApiResponse(success: false, message: e.toString());
    }
  }
}

final authVm = ChangeNotifierProvider((ref) {
  return AuthVm();
});
