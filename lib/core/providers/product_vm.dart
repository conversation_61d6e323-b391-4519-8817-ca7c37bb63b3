import 'package:bottle_king_mobile/core/core.dart';

const String getProductsState = "getProductsState";
const String productSearchState = "productSearchState";
const String productsByCategoryFilter = "productsByCategoryFilter";

class ProductVm extends BaseVm {
  List<ProductCategoryModel> _productCategories = [];
  List<ProductCategoryModel> get productCategories => _productCategories;
  List<ProductModel> _productsByCaterory = [];
  List<ProductModel> get productsByCaterory => _productsByCaterory;

  List<ProductModel> _searchProducts = [];
  List<ProductModel> get searchProducts => _searchProducts;

  List<ProductModel> _products = [];
  List<ProductModel> get products => _products;

  void clearSearchProducts() {
    _searchProducts.clear();
    reBuildUI();
  }

  Future<ApiResponse> getProductCategories() async {
    return await performApiCall(
      url: "/product/categories-with-images",
      method: apiService.get,
      onSuccess: (data) {
        _productCategories = productCategoryFromJson(
          json.encode(data["data"]),
        );
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getProductsByCategoryFilter({
    String? category,
    String? extraCategory,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/product")
      ..addQueryParameterIfNotEmpty("isMobile", "true")
      ..addQueryParameterIfNotEmpty("category", category ?? "")
      ..addQueryParameterIfNotEmpty("extra_category", extraCategory ?? "");

    printty(uriBuilder.build().toString(),
        logName: "getProductsByCategoryFilters");
    return await performApiCall(
      url: uriBuilder.build().toString(),
      busyObjectName: productsByCategoryFilter,
      method: apiService.get,
      onSuccess: (data) {
        _productsByCaterory = productFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: productFromJson(json.encode(data["data"])),
        );
      },
    );
  }

  Future<ApiResponse> getProducts({Map<String, dynamic>? args}) async {
    UriBuilder uriBuilder = UriBuilder("/product");
    args?.forEach((key, value) {
      uriBuilder.addQueryParameterIfNotEmpty(key, value);
    });
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      busyObjectName: getProductsState,
      onSuccess: (data) {
        _products = productFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: productFromJson(json.encode(data["data"])),
        );
      },
      onError: (errorMessage) {
        printty("onErrorgotcalled error: $errorMessage");
        _products.clear();
        return ApiResponse(
          success: false,
          message: errorMessage,
        );
      },
    );
  }

  Future<ApiResponse> productsBySearch({
    required String query,
  }) async {
    if (query.isEmpty) {
      clearSearchProducts();
      return ApiResponse(success: true);
    }
    return await performApiCall(
      url: "/product?search=$query",
      busyObjectName: productSearchState,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _searchProducts = productFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }
}

final productVm = ChangeNotifierProvider((ref) {
  return ProductVm();
});
