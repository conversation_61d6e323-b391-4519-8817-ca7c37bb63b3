import 'dart:convert';

List<OrderModel> orderModelFromJson(String str) =>
    List<OrderModel>.from(json.decode(str).map((x) => OrderModel.fromJson(x)));

String orderModelToJson(List<OrderModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrderModel {
  final String? id;
  final String? orderId;
  final bool? firstUserOrder;
  final String? addressLabel;
  final String? deliveryNote;
  final String? deliveryType;
  final String? customer;
  final String? status;
  final bool? paymentStatus;
  final List<Product>? products;
  final Payment? payment;
  final String? deliveryDate;
  final DateTime? createdAt;
  final DateTime? lastUpdated;
  final int? v;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phone;
  final bool? addPaper;
  final int? fare;
  final Coupon? coupon;
  final bool? addGift;
  final String? deliveryTime;
  final String? personalizedNote;
  final int? pointsGained;

  OrderModel({
    this.id,
    this.orderId,
    this.firstUserOrder,
    this.addressLabel,
    this.deliveryNote,
    this.deliveryType,
    this.customer,
    this.status,
    this.paymentStatus,
    this.products,
    this.payment,
    this.deliveryDate,
    this.createdAt,
    this.lastUpdated,
    this.v,
    this.firstname,
    this.lastname,
    this.email,
    this.phone,
    this.addPaper,
    this.fare,
    this.coupon,
    this.addGift,
    this.deliveryTime,
    this.personalizedNote,
    this.pointsGained,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        id: json["_id"],
        orderId: json["order_id"],
        firstUserOrder: json["firstUserOrder"],
        addressLabel: json["address_label"],
        deliveryNote: json["deliveryNote"],
        deliveryType: json["deliveryType"],
        customer: json["customer"],
        status: json["status"],
        paymentStatus: json["payment_status"],
        products: json["products"] == null
            ? []
            : List<Product>.from(
                json["products"]!.map((x) => Product.fromJson(x))),
        payment:
            json["payment"] == null ? null : Payment.fromJson(json["payment"]),
        deliveryDate: json["deliveryDate"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        lastUpdated: json["last_updated"] == null
            ? null
            : DateTime.parse(json["last_updated"]),
        v: json["__v"],
        firstname: json["firstname"],
        lastname: json["lastname"],
        email: json["email"],
        phone: json["phone"],
        addPaper: json["addPaper"],
        fare: json["fare"],
        coupon: json["coupon"] == null ? null : Coupon.fromJson(json["coupon"]),
        addGift: json["addGift"],
        deliveryTime: json["deliveryTime"],
        personalizedNote: json["personalizedNote"],
        pointsGained: json["pointsGained"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "order_id": orderId,
        "firstUserOrder": firstUserOrder,
        "address_label": addressLabel,
        "deliveryNote": deliveryNote,
        "deliveryType": deliveryType,
        "customer": customer,
        "status": status,
        "payment_status": paymentStatus,
        "products": products == null
            ? []
            : List<dynamic>.from(products!.map((x) => x.toJson())),
        "payment": payment?.toJson(),
        "deliveryDate": deliveryDate,
        "created_at": createdAt?.toIso8601String(),
        "last_updated": lastUpdated?.toIso8601String(),
        "__v": v,
        "firstname": firstname,
        "lastname": lastname,
        "email": email,
        "phone": phone,
        "addPaper": addPaper,
        "fare": fare,
        "coupon": coupon?.toJson(),
        "addGift": addGift,
        "deliveryTime": deliveryTime,
        "personalizedNote": personalizedNote,
        "pointsGained": pointsGained,
      };
}

class Coupon {
  final String? code;
  final bool? isRewardPointsCoupon;
  final int? pointsRedeemed;
  final int? discountPercentage;
  final num? amountPayable;

  Coupon({
    this.code,
    this.isRewardPointsCoupon,
    this.pointsRedeemed,
    this.discountPercentage,
    this.amountPayable,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) => Coupon(
        code: json["code"],
        isRewardPointsCoupon: json["isRewardPointsCoupon"],
        pointsRedeemed: json["pointsRedeemed"],
        discountPercentage: json["discountPercentage"],
        amountPayable: json["amountPayable"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "isRewardPointsCoupon": isRewardPointsCoupon,
        "pointsRedeemed": pointsRedeemed,
        "discountPercentage": discountPercentage,
        "amountPayable": amountPayable,
      };
}

class Payment {
  final String? reference;
  final num? amount;
  final String? channel;
  final String? medium;

  Payment({
    this.reference,
    this.amount,
    this.channel,
    this.medium,
  });

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        reference: json["reference"],
        amount: json["amount"],
        channel: json["channel"],
        medium: json["medium"],
      );

  Map<String, dynamic> toJson() => {
        "reference": reference,
        "amount": amount,
        "channel": channel,
        "medium": medium,
      };
}

class Product {
  final int? price;
  final int? qty;
  final String? id;
  final dynamic quickbooksId;
  final String? name;
  final String? basename;
  final String? variation;
  final String? image;
  final String? sku;

  Product({
    this.price,
    this.qty,
    this.id,
    this.quickbooksId,
    this.name,
    this.basename,
    this.variation,
    this.image,
    this.sku,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
        price: json["price"],
        qty: json["qty"],
        id: json["_id"],
        quickbooksId: json["quickbooks_id"],
        name: json["name"],
        basename: json["basename"],
        variation: json["variation"],
        image: json["image"],
        sku: json["sku"],
      );

  Map<String, dynamic> toJson() => {
        "price": price,
        "qty": qty,
        "_id": id,
        "quickbooks_id": quickbooksId,
        "name": name,
        "basename": basename,
        "variation": variation,
        "image": image,
        "sku": sku,
      };
}
