import 'package:flutter/material.dart';

const String images = "assets/images";

class AppImages {
  AppImages._();

  static const noImage = "$images/noImage.jpg";
  static const logo = "$images/logo.png";
  static const logo2 = "$images/logo2.png";
  static const logoBlack = "$images/logoBlack.png";

  // Onboarding
  static const onb0 = "$images/onboard/onb0.png";
  static const onb1 = "$images/onboard/onb1.png";
  static const onb2 = "$images/onboard/onb2.png";

  static const v0 = "$images/onboard/v0.png";
  static const v1 = "$images/onboard/v1.png";
  static const v2 = "$images/onboard/v2.png";

  static const cat = "$images/cat.png";
  static const gift = "$images/gift.png";

  static const product = "$images/product.png";
  static const coin = "$images/coin.png";
}

// Image Helper
SizedBox imageHelper(String image,
    {double? height, double? width, BoxFit? fit}) {
  return SizedBox(
      height: height,
      width: width,
      child: Image.asset(
        image,
        fit: fit,
      ));
}
